# 系统设计文档

## 概述

本系统设计文档为AI小说生成器提供精简的技术方案，专为个人Mac笔记本使用优化。系统采用轻量级单体架构，专注核心小说生成功能，支持多模型AI融合生成，无需用户管理和复杂的协作功能。

## 系统架构

### 整体架构

系统采用简洁的单体架构：
- **前端**：本地Web界面
- **后端**：本地Python服务
- **数据库**：本地MySQL
- **AI集成**：直接调用OpenAI API

### 技术栈选择

#### 前端技术栈
- **框架**：React 18 + TypeScript
- **状态管理**：React Context
- **UI组件库**：Ant Design
- **构建工具**：Vite
- **部署**：本地静态服务

#### 后端技术栈
- **框架**：FastAPI (Python)
- **数据库**：MySQL 8.0
- **AI集成**：OpenAI Compatible API
- **部署**：本地直接运行

#### 本地环境
- **操作系统**：macOS (Apple M3)
- **Python**：3.12
- **Node.js**：18+

## 核心模块设计



### 2. 人名管理模块

#### 功能设计
- 人名库管理（按文化背景分类）
- 自定义人名添加
- 人名含义解析
- 重名检测

#### 数据模型
```sql
CREATE TABLE name_library (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    culture VARCHAR(50),
    gender VARCHAR(10),
    meaning TEXT,
    frequency INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE custom_names (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    meaning TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 人物小传模块

#### 功能设计
- 人物基础信息管理
- 人物关系图谱
- 人物成长轨迹
- 人物性格分析

#### 数据模型
```sql
CREATE TABLE characters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT REFERENCES novels(id),
    name VARCHAR(100) NOT NULL,
    age INTEGER,
    gender VARCHAR(10),
    background TEXT,
    personality_traits JSON,
    character_arc JSON,
    relationships JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. 大纲生成模块

#### 功能设计
- 故事结构模板（三幕式、英雄之旅等）
- 章节大纲自动生成
- 情节转折点设计
- 冲突升级机制

#### 处理流程
1. 用户输入基本设定
2. AI分析故事类型和风格
3. 生成故事结构框架
4. 细化章节大纲
5. 用户交互式调整

### 5. 章节扩写模块

#### 功能设计
- 智能扩写引擎
- 写作风格控制
- 情节连贯性检查
- 多版本生成

#### AI处理流程
```python
class ChapterExpander:
    def __init__(self, model_config):
        self.llm = OpenAI(**model_config)
        
    async def expand_chapter(self, outline, style_guide, word_count):
        prompt = self._build_expansion_prompt(outline, style_guide, word_count)
        response = await self.llm.agenerate(prompt)
        return self._post_process(response)
```

### 6. 世界观设定模块

#### 功能设计
- 世界观模板库
- 地理、历史、文化设定
- 魔法/科技体系构建
- 种族和文明设定

#### 数据模型
```sql
CREATE TABLE world_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT REFERENCES novels(id),
    world_name VARCHAR(255),
    geography JSON,
    history JSON,
    cultures JSON,
    magic_system JSON,
    technology_level VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. 情节冲突管理模块

#### 功能设计
- 冲突类型分类（内在、外在、人际）
- 冲突强度评级
- 冲突解决追踪
- 悬念设置工具

### 8. 写作风格控制模块

#### 功能设计
- 风格模板（悬疑、浪漫、史诗等）
- 语言风格分析
- 一致性检查
- 模仿学习功能

### 9. 版本管理模块

#### 功能设计
- 章节版本控制
- 变更历史追踪
- 版本对比功能
- 回滚机制

#### 数据模型
```sql
CREATE TABLE chapter_versions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chapter_id INT REFERENCES chapters(id),
    version_number INTEGER,
    content TEXT,
    change_summary TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 10. 协作功能模块

#### 功能设计
- 实时协作编辑
- 评论和建议系统
- 角色权限管理
- 变更通知

### 11. 灵感收集模块

#### 功能设计
- 灵感素材库
- 标签分类系统
- 关联推荐
- 快速引用

### 12. 读者反馈分析模块

#### 功能设计
- 反馈收集接口
- 情感分析
- 热点识别
- 改进建议

### 13. 出版格式导出模块

#### 支持格式
- EPUB（电子书标准格式）
- PDF（打印就绪格式）
- DOCX（Word文档）
- Markdown（通用标记格式）

## API设计

### RESTful API结构

#### 用户管理API
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/users/profile` - 获取用户信息

#### 小说管理API
- `POST /api/novels` - 创建小说
- `GET /api/novels/{id}` - 获取小说详情
- `PUT /api/novels/{id}` - 更新小说信息
- `DELETE /api/novels/{id}` - 删除小说

#### 章节管理API
- `POST /api/novels/{novel_id}/chapters` - 创建章节
- `GET /api/chapters/{id}` - 获取章节内容
- `PUT /api/chapters/{id}` - 更新章节内容
- `POST /api/chapters/{id}/expand` - AI扩写章节
- `POST /api/novels/{novel_id}/chapters/generate-from-outline` - 根据大纲生成章节

#### AI功能API
- `POST /api/ai/generate-outline` - 生成大纲
- `POST /api/ai/generate-names` - 生成人名
- `POST /api/ai/analyze-style` - 分析写作风格
- `POST /api/ai/generate-character` - AI生成人物
- `POST /api/ai/generate-world-settings` - AI生成世界观设定
- `POST /api/ai/generate-conflicts` - AI生成冲突建议
- `POST /api/ai/optimize-writing` - AI优化写作风格
- `POST /api/ai/suggest-improvements` - AI改进建议

#### AI配置管理API
- `GET /api/ai-configs` - 获取AI配置列表
- `POST /api/ai-configs` - 创建AI配置
- `PUT /api/ai-configs/{id}` - 更新AI配置
- `DELETE /api/ai-configs/{id}` - 删除AI配置
- `POST /api/ai-configs/{id}/set-default` - 设为默认配置

#### AI调用记录API
- `GET /api/novels/{novel_id}/ai-logs` - 获取AI调用记录
- `GET /api/ai-logs/{id}` - 获取AI调用详情

## 数据库设计

### 核心表结构

#### novels（小说主表）
```sql
CREATE TABLE novels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    genre VARCHAR(50),
    target_audience VARCHAR(50),
    status VARCHAR(20) DEFAULT 'draft',
    word_count INTEGER DEFAULT 0,
    ai_model_config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### chapters（章节表）
```sql
CREATE TABLE chapters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT REFERENCES novels(id),
    chapter_number INTEGER,
    title VARCHAR(255),
    content TEXT,
    outline TEXT,
    status VARCHAR(20) DEFAULT 'draft',
    word_count INTEGER DEFAULT 0,
    ai_generated BOOLEAN DEFAULT FALSE,
    ai_model_used VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ai_configs（AI配置表）
```sql
CREATE TABLE ai_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    api_base VARCHAR(255),
    api_key VARCHAR(255),
    model VARCHAR(100),
    max_tokens INTEGER,
    temperature FLOAT,
    top_p FLOAT,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ai_logs（AI调用记录表）
```sql
CREATE TABLE ai_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT REFERENCES novels(id),
    operation_type VARCHAR(50),
    prompt TEXT,
    response TEXT,
    model_used VARCHAR(100),
    tokens_used INTEGER,
    duration_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 本地部署方案

### 环境准备
```bash
# 安装MySQL
brew install mysql
brew services start mysql

# 创建数据库
mysql -u root -p
CREATE DATABASE novel_generator;

# 安装Python依赖
pip install fastapi uvicorn sqlalchemy mysql-connector-python langchain openai

# 安装Node.js依赖
cd frontend && npm install
```

### 启动命令
```bash
# 启动后端
python main.py

# 启动前端
cd frontend && npm run dev
```

## 基础配置

### 数据库连接
```python
# config.py
DATABASE_URL = "mysql+mysqlconnector://root:password@localhost/novel_generator"
OPENAI_API_KEY = "your_api_key_here"
```

## 数据备份

### 简单备份方案
```bash
# 导出数据库
mysqldump -u root -p novel_generator > backup.sql

# 导入数据库
mysql -u root -p novel_generator < backup.sql
```

## 扩展性考虑

### 水平扩展
- 无状态服务设计
- 数据库分片
- 微服务拆分

### 功能扩展
- 插件化架构
- 第三方集成
- 自定义工作流